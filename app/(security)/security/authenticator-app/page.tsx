"use client";

import React, { useState } from "react";
import { AppButton } from "@/components";
import {
  ModalLinkAuthenticator,
  ModalConfirmChangeAuthenticationApp,
  ModalConfirmRemoveAuthenticationApp,
  ModalSelectVerificationMethod,
  ModalSelectUnavailableMethods,
  ModalTwoFactorVerification,
} from "@/modals";
import { AuthenticatorAppIcon, EditIcon25, TrashIcon25 } from "@/assets/icons";
import { useSelector } from "react-redux";
import { RootState, store } from "@/store";
import { useRouter } from "next/navigation";
import { successMsg } from "@/libs/toast";
import rf from "@/services/RequestFactory";
import { getSecurityCheckupState } from "@/store/user.store";
import Link from "next/link";

const ContentHasAuthenticatorApp = () => {
  const [isShowModalConfirmChange, setIsShowModalConfirmChange] =
    useState<boolean>(false);
  const [isShowModalConfirmRemove, setIsShowModalConfirmRemove] =
    useState<boolean>(false);
  const [
    isShowModalSelectVerificationMethod,
    setIsShowModalSelectVerificationMethod,
  ] = useState<boolean>(false);
  const [
    isShowModalSelectUnavailableMethod,
    setIsShowModalSelectUnavailableMethod,
  ] = useState<boolean>(false);
  const [isShowModalEnterCode, setIsShowModalEnterCode] =
    useState<boolean>(false);
  const router = useRouter();

  const handleRequestEmailForDisable = async () => {
    await rf.getRequest("AuthRequest").requestEmailCodeToDisable2FA();
  };

  const handleDisableCompleted = async (data: {
    emailCode?: string;
    authenticatorCode?: string;
  }) => {
    await rf.getRequest("AuthRequest").verifyDisableCode({
      emailCode: data.emailCode,
      authenticatorCode: data.authenticatorCode,
    });
    store.dispatch(getSecurityCheckupState());
    successMsg("2FA disabled successfully");
    setIsShowModalEnterCode(false);
    router.push("/my/security");
  };

  return (
    <div className="border-white-100 flex items-center justify-between gap-4 rounded-[8px] border px-3 py-4">
      <div className="flex items-center gap-4">
        <AuthenticatorAppIcon />
        <div>
          <div className="heading-sm-medium-16">Authenticator App</div>
          {/*<div className="text-white-500 body-md-regular-14">*/}
          {/*  Added: May 28, 2025*/}
          {/*</div>*/}
        </div>
      </div>

      <div className="flex items-center gap-6">
        {/* <div
          className="cursor-pointer"
          onClick={() => setIsShowModalConfirmChange(true)}
        >
          <EditIcon25 />
        </div> */}
        <div
          className="cursor-pointer"
          onClick={() => setIsShowModalConfirmRemove(true)}
        >
          <TrashIcon25 />
        </div>
      </div>

      {/* Change authenticator app modal */}
      {isShowModalConfirmChange && (
        <ModalConfirmChangeAuthenticationApp
          isOpen={isShowModalConfirmChange}
          onClose={() => setIsShowModalConfirmChange(false)}
          onConfirm={() => {
            setIsShowModalEnterCode(true);
            setIsShowModalConfirmChange(false);
          }}
        />
      )}
      {/* Remove authenticator app modal */}
      {isShowModalConfirmRemove && (
        <ModalConfirmRemoveAuthenticationApp
          isOpen={isShowModalConfirmRemove}
          onClose={() => setIsShowModalConfirmRemove(false)}
          onConfirm={() => {
            setIsShowModalConfirmRemove(false);
            setIsShowModalEnterCode(true);
          }}
        />
      )}
      {/* Enter verification code modal for disable */}
      {isShowModalEnterCode && (
        <ModalTwoFactorVerification
          isOpen={isShowModalEnterCode}
          onClose={() => setIsShowModalEnterCode(false)}
          requireEmailVerification={true}
          requireAuthenticatorCode={true}
          title="Disable Authenticator App"
          description="Enter both email and authenticator codes to disable 2FA"
          requestEmail={handleRequestEmailForDisable}
          onCompleted={handleDisableCompleted}
        />
      )}
      {/* Select verification method modal */}
      {isShowModalSelectVerificationMethod && (
        <ModalSelectVerificationMethod
          isOpen={isShowModalSelectVerificationMethod}
          onClose={() => setIsShowModalSelectVerificationMethod(false)}
          onReset={() => {
            setIsShowModalSelectUnavailableMethod(true);
            setIsShowModalSelectVerificationMethod(false);
          }}
        />
      )}
      {/* Select unavailable method modal */}
      {isShowModalSelectUnavailableMethod && (
        <ModalSelectUnavailableMethods
          isOpen={isShowModalSelectUnavailableMethod}
          onClose={() => setIsShowModalSelectUnavailableMethod(false)}
        />
      )}
    </div>
  );
};

const ContentNotLinkAuthenticatorApp = () => {
  const [isShowModalLinkAuth, setIsShowModalLinkAuth] =
    useState<boolean>(false);
  const [isShowModalEnterCode, setIsShowModalEnterCode] =
    useState<boolean>(false);
  const [authenticatorSecretCode, setAuthenticatorSecretCode] =
    useState<string>("");
  const [authenticatorQRCode, setAuthenticatorQRCode] = useState<string>("");
  const router = useRouter();

  const handleRequestEmailForEnable = async () => {
    await rf.getRequest("AuthRequest").requestEmailCodeToEnable2FA();
  };

  const handleEnableCompleted = async (data: {
    emailCode?: string;
    authenticatorCode?: string;
  }) => {
    const response = await rf.getRequest("AuthRequest").verifyEnableCode({
      emailCode: data.emailCode,
    });
    successMsg("Verify code successfully");
    setAuthenticatorSecretCode(response.data?.secret || "");
    setAuthenticatorQRCode(response.data?.qrCode || "");
    setIsShowModalLinkAuth(true);
    setIsShowModalEnterCode(false);
  };

  return (
    <>
      <div className="mx-auto mt-[50px] flex w-full max-w-[443px] flex-1 flex-col items-center justify-center md:mt-[150px]">
        <div className="heading-lg-medium-24 mb-4 text-center text-[16px] lg:text-[24px]">
          Authenticator App
        </div>

        <div className="body-md-regular-14 text-white-500 text-center">
          Instead of waiting for text messages, get verification codes from an
          authenticator app like Google Authenticator. It works even if your
          phone is offline.
        </div>
        <AppButton
          variant="buy"
          size="large"
          className="mt-8 w-full"
          onClick={() => setIsShowModalEnterCode(true)}
        >
          Enable Authenticator App
        </AppButton>

        <Link
          target="_blank"
          rel="noopener noreferrer"
          href={
            "https://chromewebstore.google.com/detail/authenticator/bhghoamapcdpbohphigoooaddinpkbai"
          }
          className="body-md-medium-14 text-brand-500 mt-4 cursor-pointer"
        >
          Download Authenticator App
        </Link>
      </div>

      {isShowModalEnterCode && (
        <ModalTwoFactorVerification
          isOpen={isShowModalEnterCode}
          onClose={() => setIsShowModalEnterCode(false)}
          requireEmailVerification={true}
          requireAuthenticatorCode={false}
          title="Enable Authenticator App"
          description="Enter email verification code to enable 2FA"
          requestEmail={handleRequestEmailForEnable}
          onCompleted={handleEnableCompleted}
        />
      )}

      {isShowModalLinkAuth && (
        <ModalLinkAuthenticator
          isOpen={isShowModalLinkAuth}
          authenticatorSecretCode={authenticatorSecretCode}
          qrCode={authenticatorQRCode}
          onClose={() => {
            setIsShowModalLinkAuth(false);
            store.dispatch(getSecurityCheckupState());
            router.push("/my/security");
          }}
        />
      )}
    </>
  );
};

const AuthenticatorAppPage = () => {
  const securityCheckupState = useSelector(
    (state: RootState) => state.user.securityCheckupState
  );

  return (
    <div className="mt-4">
      <div className="heading-lg-medium-24 mb-4">Authenticator App</div>

      {securityCheckupState?.twoFactorAuthentication ? (
        <ContentHasAuthenticatorApp />
      ) : (
        <ContentNotLinkAuthenticatorApp />
      )}
    </div>
  );
};

export default AuthenticatorAppPage;
