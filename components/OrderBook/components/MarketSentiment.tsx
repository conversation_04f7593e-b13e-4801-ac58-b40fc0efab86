import React, { useState, useEffect, useRef } from "react";

// Custom hook to throttle MarketSentiment updates
const useMarketSentimentThrottle = (
  bidPercent: number,
  askPercent: number,
  interval = 1000
) => {
  const [throttledValues, setThrottledValues] = useState({
    bidPercent,
    askPercent,
  });

  const latestValuesRef = useRef({ bidPercent, askPercent });
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastUpdateTimeRef = useRef<number>(0);

  // Update the latest values ref whenever the values change
  useEffect(() => {
    latestValuesRef.current = { bidPercent, askPercent };

    if (!timeoutRef.current) {
      const now = Date.now();
      const timeSinceLastUpdate = now - lastUpdateTimeRef.current;
      const nextUpdateDelay = Math.max(0, interval - timeSinceLastUpdate);

      timeoutRef.current = setTimeout(() => {
        setThrottledValues(latestValuesRef.current);
        lastUpdateTimeRef.current = Date.now();
        timeoutRef.current = null;

        timeoutRef.current = setInterval(() => {
          setThrottledValues(latestValuesRef.current);
          lastUpdateTimeRef.current = Date.now();
        }, interval);
      }, nextUpdateDelay);
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        clearInterval(timeoutRef.current);
        timeoutRef.current = null;
      }
    };
  }, [bidPercent, askPercent, interval]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        clearInterval(timeoutRef.current);
      }
    };
  }, []);

  return throttledValues;
};

export const MarketSentiment = React.memo(
  ({ bidPercent, askPercent }: { bidPercent: number; askPercent: number }) => {
    // Throttle the updates to once per second (1000ms)
    const { bidPercent: throttledBidPercent, askPercent: throttledAskPercent } =
      useMarketSentimentThrottle(bidPercent, askPercent, 1000);

    return (
      <div className="body-sm-regular-12 flex items-center gap-1 px-2 py-1">
        <div className="text-[color:var(--up-color)]">B</div>
        <div className="text-[color:var(--up-color)]">
          {throttledBidPercent}%
        </div>

        <div className="flex h-[4px] w-full justify-end rounded-full bg-[color:var(--up-color)]">
          <div
            className="h-[4px] rounded-r-full bg-[color:var(--down-color)]"
            style={{ width: `${throttledAskPercent}%` }}
          />
        </div>

        <div className="text-[color:var(--down-color)]">
          {throttledAskPercent}%
        </div>
        <div className="text-[color:var(--down-color)]">S</div>
      </div>
    );
  }
);

MarketSentiment.displayName = "MarketSentiment";
