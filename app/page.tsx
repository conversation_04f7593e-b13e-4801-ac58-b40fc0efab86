"use client";

import { <PERSON><PERSON> } from "@/layouts";
import { App<PERSON><PERSON>on, AppNumber, AppQRCode } from "@/components";
import React, { useEffect, useMemo, useState } from "react";
import Link from "next/link";
import { EyeIcon, EyeCloseIcon, BookIcon } from "@/assets/icons";
import { useSelector } from "react-redux";
import { RootState } from "../store/index";
import { useMediaQuery } from "react-responsive";
import { useSearchParams } from "next/navigation";
import useAccountBalance from "@/hooks/useAccountBalance";
import BigNumber from "bignumber.js";
import PairMarketLanding from "@/components/PairMarketLanding";
import { KYC_STATUS } from "../constants/common";
import { MarketExplorer } from "@/components/PairMarket/MarketExplorer";
import Image from "next/image";
import { maskEmail } from "@/utils/format";
import config from "../config/index";

const Balance = ({ isHomePage }: { isHomePage?: boolean }) => {
  const [isShowBalance, setIsShowBalance] = useState<boolean>(true);
  const { accountBalances } = useAccountBalance({});

  const totalBalanceUsd = useMemo(() => {
    return accountBalances.reduce((acc, item) => {
      return acc.plus(item.totalBalanceInUsd || 0);
    }, new BigNumber(0));
  }, [accountBalances]);

  if (
    !totalBalanceUsd ||
    new BigNumber(totalBalanceUsd || 0).isZero() ||
    new BigNumber(totalBalanceUsd).isNaN()
  ) {
    return <></>;
  }

  return (
    <div className={`flex flex-col gap-2 ${isHomePage ? "px-4 pb-4" : ""}`}>
      <div className="flex items-center justify-between">
        <div className="heading-sm-medium-16 text-white-500 flex items-center gap-2">
          Estimated Balance
          <div
            onClick={() => setIsShowBalance(!isShowBalance)}
            className="cursor-pointer"
          >
            {isShowBalance ? <EyeIcon /> : <EyeCloseIcon />}
          </div>
        </div>
      </div>

      <div>
        <div className=" flex gap-2">
          <div className="heading-xlg-semibold-32">
            <AppNumber
              value={totalBalanceUsd}
              isFormatLargeNumber={false}
              isHide={!isShowBalance}
            />
          </div>

          <div className="heading-sm-medium-16 flex cursor-pointer items-center gap-1">
            USDT
          </div>
        </div>

        <div className="heading-sm-medium-16 text-white-800 flex">
          ≈
          <AppNumber
            value={totalBalanceUsd}
            isFormatLargeNumber={false}
            isForUSD
            isHide={!isShowBalance}
          />
        </div>
      </div>
      {/* <div className="flex items-center gap-2">
        <div className="heading-sm-medium-16 text-white-500 flex items-center gap-2">
          Today&apos;s PnL
          <Tooltip
            placement="top"
            overlay={
              <div className="body-sm-regular-12 max-w-[285px]">
                Today&apos;s PNL = Current asset total - Today&apos;s initial
                asset total - Today&apos;s net transfer and deposit.*The
                calculation of PNL currently does not include data for Alpha
                tokens. The date is only for your reference, there is no
                guarantee that the data is absolutely accurate.
              </div>
            }
          >
            <InforIcon className="h-4 w-4 cursor-pointer" />
          </Tooltip>
        </div>
        {isShowBalance ? (
          <div className="heading-sm-medium-16 text-green-500">
            +$530.87 (1.69%)
          </div>
        ) : (
          <div className="heading-sm-medium-16 text-white-500">******</div>
        )}
      </div> */}
    </div>
  );
};

const ContentMobile = () => {
  const userId = useSelector((state: RootState) => state.user.userInfo?.id);
  const userInfo = useSelector((state: RootState) => state.user.userInfo);
  const loading = useSelector((state: RootState) => state.user.loading);
  const kycStatus = useSelector(
    (state: RootState) => state.user.userInfo?.kycStatus
  );

  const _renderContentAuth = () => {
    if (loading) return <></>;
    if (!userId) {
      return (
        <div className="relative -mt-[50px]">
          <img
            src={"/images/bg-homepage-mobile.png"}
            alt="BgHomepageMobile"
            className="aspect-square w-full"
          />
          <div className="absolute bottom-0 left-0 right-0 flex flex-col gap-4 p-4">
            <div className="heading-lg-medium-24">
              <span className="bg-gradient-text-green">Get Verified</span>{" "}
              <span className="">& Start Your Crypto</span>{" "}
              <span className="bg-gradient-text"> Journey</span>
            </div>
            <div className="grid grid-cols-2 gap-3">
              <Link href="/login">
                <AppButton variant="secondary" size="medium" className="w-full">
                  Sign In
                </AppButton>
              </Link>
              <Link href="/register">
                <AppButton variant="buy" size="medium" className="w-full">
                  Sign Up
                </AppButton>
              </Link>
            </div>
          </div>
        </div>
      );
    }
    return (
      <div className="relative -mt-[50px]">
        <img
          src={"/images/bg-homepage-mobile.png"}
          alt="BgHomepageMobile"
          className="z-[-1] aspect-square w-full"
        />
        <div className="-mt-[150px] flex gap-4 p-4">
          <Image
            src={"/images/AvatarDefault.png"}
            alt="avatar"
            width={64}
            height={64}
            className="aspect-square h-[64px] !w-[64px] rounded-[8px]"
          />
          <div className="flex flex-col justify-center">
            <div className="lg:heading-lg-medium-24 text-[16px]">
              {userInfo?.nickname || userInfo?.name || "--"}
            </div>
            <div className="flex items-center gap-2">
              {maskEmail(userInfo?.email || "--")}
            </div>
          </div>
        </div>

        <Balance isHomePage />

        {kycStatus !== KYC_STATUS.VERIFIED && (
          <div className="flex items-center gap-6 px-4 pb-4">
            <div className="flex-1">
              <div className="body-sm-regular-12 text-white-500 mb-1">
                Onboard task
              </div>
              <div className="heading-lg-medium-24">
                Complete your identity verification
              </div>
            </div>
            <Link href="/my/kyc">
              <AppButton
                variant="buy"
                size="small"
                className="px-4 text-[10px]"
              >
                Verify Now
              </AppButton>
            </Link>
          </div>
        )}
      </div>
    );
  };

  return (
    <div>
      <Header />
      <div className="pt-[50px]">{_renderContentAuth()}</div>
      <div>
        <MarketExplorer isHomePage />
      </div>
    </div>
  );
};

export default function Home() {
  const userId = useSelector((state: RootState) => state.user.userInfo?.id);
  const kycStatus = useSelector(
    (state: RootState) => state.user.userInfo?.kycStatus
  );
  const isMobileBreakpoint = useMediaQuery({ query: "(max-width: 992px)" });
  const [isMounted, setIsMounted] = useState<boolean>(false);

  const params = useSearchParams();

  useEffect(() => {
    if (params) {
      const referrerCode = params.get("ref");
      if (referrerCode) {
        localStorage.setItem("referrerCode", referrerCode);
      }
    }
  }, [params]);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const _renderContentAuth = () => {
    if (!userId) {
      return (
        <div className="mt-8 flex gap-4">
          <Link href="/register">
            <AppButton variant="buy" size="large" className="w-[140px]">
              Sign Up
            </AppButton>
          </Link>
          <Link href="/login">
            <AppButton variant="secondary" size="large" className="w-[140px]">
              Sign In
            </AppButton>
          </Link>
        </div>
      );
    }
    return (
      <div className="mt-8">
        <Balance />
        <div className="mt-8 flex items-center gap-4">
          {kycStatus !== KYC_STATUS.VERIFIED && (
            <Link href="/my/kyc">
              <AppButton variant="buy" size="large" className="w-[140px]">
                Verify Now
              </AppButton>
            </Link>
          )}

          <Link href={config.docsUrl} target="_blank">
            <div className="body-md-medium-14 mx-4 flex cursor-pointer items-center gap-1">
              <BookIcon />
              Read Tutorial
            </div>
          </Link>
        </div>
      </div>
    );
  };

  if (!isMounted) return <></>;

  if (isMobileBreakpoint) {
    return <ContentMobile />;
  }

  return (
    <div>
      <Header />
      <div className="pt-[50px]">
        <div className="mx-auto max-w-[1440px]">
          <div className="flex min-h-[646px] max-w-[1440px] items-center bg-[url('/images/BgHomepage.png')] bg-cover bg-top bg-no-repeat px-[100px] py-[56px]">
            <div>
              <div>
                <div className="max-w-[584px] text-[72px] font-extrabold leading-[1.2]">
                  <span className="bg-gradient-text-green ">Get Verified</span>{" "}
                  <span className="bg-gradient-text">& Start Your</span>{" "}
                  <span className="bg-gradient-text"> Crypto Journey</span>
                </div>
              </div>
              {_renderContentAuth()}
            </div>
          </div>

          <div className="flex items-center justify-between px-[100px] pb-[70px]">
            <div className="flex items-center gap-6">
              <div className="border-white-100 flex flex-col items-center rounded-[16px] border p-4">
                <AppQRCode
                  color="rgba(255, 255, 255, 1)"
                  className="h-[166px] w-[166px]"
                  value={"Download App"}
                />

                <div className="body-md-regular-14 text-white-500 mt-2">
                  Scan to Download App
                </div>
                <div className="heading-md-semibold-18">iOS and Android</div>
              </div>
              <div className="heading-lg-medium-24 max-w-[186px]">
                Trade on the go. Anywhere, anytime.
              </div>
            </div>

            <div className="flex flex-col justify-between gap-8">
              <PairMarketLanding />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
