"use client";

import { CheckboxCheckedIcon, CheckboxIcon, GoogleIcon } from "@/assets/icons";
import { AppButton, EmailInput } from "@/components";
import Link from "next/link";
import { useState, useRef } from "react";
import { EStep } from "../steps";
import { handleSocialLogin } from "@/utils/auth";
import { ELoginProvider } from "@/types/auth";
import { EmailInputRef } from "@/components/EmailInput";

type StepInputEmailProps = {
  setStep: (step: EStep) => void;
  mailOrPhone: string;
  setMailOrPhone: (mailOrPhone: string) => void;
};

export const StepInputEmail: React.FC<StepInputEmailProps> = ({
  setStep,
  mailOrPhone,
  setMailOrPhone,
}) => {
  const [isAgree, setIsAgree] = useState<boolean>(true);
  const emailInputRef = useRef<EmailInputRef>(null);

  const onNext = async () => {
    if (!isAgree) return;

    if (!emailInputRef.current?.validate()) {
      return;
    }

    setStep(EStep.InputPassword);
  };

  const handleAgreeToggle = () => {
    setIsAgree(!isAgree);
  };

  const isFormValid = isAgree && mailOrPhone.trim() !== "";

  return (
    <>
      <div className="heading-lg-semibold-24 lg:text-[32px]">
        Welcome to VDAX
      </div>

      <EmailInput
        ref={emailInputRef}
        value={mailOrPhone}
        onChange={setMailOrPhone}
        onSubmit={onNext}
        autoFocus
        autoComplete="off"
      />

      <div className="flex items-center gap-2">
        <div onClick={handleAgreeToggle} className="w-6 cursor-pointer">
          {isAgree ? <CheckboxCheckedIcon /> : <CheckboxIcon />}
        </div>
        <div className="body-md-regular-14 text-white-800">
          By creating an account, I agree to VDAX&apos;s{" "}
          <a href="#" target="_blank" className="underline">
            Terms of Service
          </a>{" "}
          and{" "}
          <a href="#" target="_blank" className="underline">
            Privacy Policy
          </a>
        </div>
      </div>

      <AppButton
        variant="buy"
        size="large"
        onClick={onNext}
        disabled={!isFormValid}
      >
        Next
      </AppButton>

      <div className="flex items-center gap-1">
        <div className="bg-white-100 h-[1px] w-[calc(50%-4px)]"></div>
        <div className="body-md-regular-14 text-white-800">Or</div>
        <div className="bg-white-100 h-[1px] w-[calc(50%-4px)]"></div>
      </div>

      <AppButton
        variant="outline"
        size="large"
        className="relative"
        onClick={() => handleSocialLogin(ELoginProvider.GOOGLE)}
      >
        <GoogleIcon className="absolute left-4" /> Continue with Google
      </AppButton>

      <div className="flex justify-center py-2.5">
        <Link href="/login">
          <div className="body-md-medium-14 text-brand-500 cursor-pointer">
            Sign In
          </div>
        </Link>
      </div>
    </>
  );
};
