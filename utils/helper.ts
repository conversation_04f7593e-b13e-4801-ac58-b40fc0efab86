import BigNumber from "bignumber.js";
import { errorMsg, successMsg } from "@/libs/toast";
import copy from "copy-to-clipboard";
import { EOrderSide } from "@/components/OrderForm/OrderFormMobile";
import { isZero } from "./number";
import { EOrderStopCondition, TOpenOrder, TOrderHistory } from "@/types/order";
import { EOrderType } from "@/components/OrderForm";
import validate from "bitcoin-address-validation";
import { isAddress } from "ethers";
import { isNull, isUndefined } from "lodash";

export const copyToClipboard = (message: string) => {
  try {
    copy(message);
    successMsg("Copied!");
  } catch (error: any) {
    errorMsg(error.message || "Something went wrong!");
  }
};

export const getDecimalPlaces = (value: string | number): number => {
  const bn = new BigNumber(value);
  return bn.decimalPlaces() ?? 0;
};
export const isMobile = () => {
  const isBrowser = typeof window !== "undefined";
  return (
    isBrowser &&
    typeof navigator !== "undefined" &&
    /Mobi|Android|iPhone|iPad|iPod/i.test(navigator.userAgent)
  );
};

export const getPriceStyle = (priceChange: string | null) => {
  if (!priceChange || isZero(priceChange)) return "var(--color-white-900)";

  if (BigNumber(priceChange).isGreaterThan(0)) {
    return "var(--up-color)";
  }

  return "var(--down-color)";
};

export const getSideColor = (orderSide: EOrderSide) => {
  if (orderSide?.toUpperCase() == EOrderSide.BUY) {
    return "var(--up-color)";
  }

  return "var(--down-color)";
};

export const sleep = (ms: number) => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

export const removeCommas = (numStr: string) => {
  return numStr.replace(/,/g, "");
};

export const calculateFilledPercent = (
  filled: string | null,
  total: string
) => {
  if (!filled || isZero(filled)) return 0;

  return BigNumber(filled).dividedBy(total).multipliedBy(100).toFixed(2);
};

export const isInvalidNumber = (value: string | number) => {
  return !value || isNaN(Number(value)) || Number(value) <= 0;
};

export const filterParams = (params: any) => {
  return Object.fromEntries(Object.entries(params).filter(([_, v]) => v));
};

export const isValidEmail = (email: string) => {
  const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return regex.test(email);
};

export const getTriggerConditionDisplay = (
  order: TOrderHistory | TOpenOrder
) => {
  if (
    order?.type === EOrderType.STOP_LIMIT ||
    order?.type === EOrderType.STOP_MARKET
  ) {
    if (order?.stop_price && order?.stop_condition) {
      const symbol =
        order?.stop_condition === EOrderStopCondition.GREATER_THAN
          ? ">="
          : "<=";
      return symbol;
    }
  }
  return "-";
};

export const getOrderTypeDisplay = (type: EOrderType) => {
  switch (type) {
    case EOrderType.LIMIT:
      return "Limit";
    case EOrderType.MARKET:
      return "Market";
    case EOrderType.STOP_LIMIT:
      return "Stop Limit";
    case EOrderType.STOP_MARKET:
      return "Stop Market";
    default:
      return type;
  }
};

export function isValidEvmAddress(address: string): boolean {
  try {
    return isAddress(address); // returns true if valid
  } catch {
    return false;
  }
}

export const isValidBitcoinAddress = (address: string) => {
  return validate(address);
};

export const isInValidValue = (value: string | number) => {
  return value == "" || isUndefined(value) || isNull(value);
};

export const getUserLevelDisplay = (level: number) => {
  switch (level) {
    case 0:
      return "Regular User";
    case 1:
      return "VIP 1";
    case 2:
      return "VIP 2";
    case 3:
      return "VIP 3";
    default:
      return "Regular User";
  }
};
