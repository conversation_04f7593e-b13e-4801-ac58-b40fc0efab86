import { useMemo } from "react";
import { convertToUSDTPrice } from "@/utils/currency";
import { useMultipleTickers } from "./useTicker";

export const useUSDPrice = (
  currentPrice: string | null | undefined,
  quoteAsset: string | undefined
): string | null => {
  const { tickers, renderCount } = useMultipleTickers();

  const conversionSymbol = quoteAsset
    ? `${quoteAsset.toUpperCase()}USDT`
    : null;
  const conversionTicker = conversionSymbol ? tickers[conversionSymbol] : null;

  return useMemo(() => {
    return convertToUSDTPrice(currentPrice || null, quoteAsset, tickers);
  }, [currentPrice, quoteAsset, conversionTicker?.lastPrice, renderCount]);
};
