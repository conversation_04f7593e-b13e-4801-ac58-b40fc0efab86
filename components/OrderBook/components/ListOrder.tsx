import React, { useCallback, useState } from "react";
import { EOrderbook, OrderBookLevel } from "@/types/OrderBook";
import BigNumber from "bignumber.js";
import { getDecimalPlaces } from "@/utils/helper";
import { useProcessedOrderbook } from "../hooks/useProcessedOrderbook";
import { AppBroadcast, BROADCAST_EVENTS } from "@/libs/broadcast";
import AppNumber from "@/components/AppNumber";
import { usePairContext } from "@/app/trade/[symbol]/provider";
import { formatNumber, formatNumberExact, formatPrice } from "@/utils/format";
import { useSelector } from "react-redux";
import { RootState } from "@/store/index";
import Tippy from "@tippyjs/react";
import { TYPE_LAYOUT } from "@/constants/common";
import { useMediaQuery } from "react-responsive";

const OrderRow = React.memo(
  ({
    item,
    percentWidth,
    decimal,
    orderbookType,
    quantityDecimal,
    isHideTotal = false,
    index,
    onItemClick,
  }: {
    item: {
      price: string;
      amount: string;
      total: string;
      cumulative: string;
    };
    quantityDecimal: number;
    percentWidth: string;
    decimal: number;
    orderbookType: EOrderbook;
    isHideTotal?: boolean;
    index: number;
    onItemClick: (index: number, price: string) => void;
  }) => {
    let textColor = "text-[color:var(--down-color)]";
    let bgColorVar = "var(--bg-down-color)";

    if (orderbookType === EOrderbook.BID) {
      textColor = "text-[color:var(--up-color)]";
      bgColorVar = "var(--bg-up-color)";
    }

    const handleClickItem = () => {
      if (!item.price) return;
      onItemClick(index, item.price);
    };

    return (
      <div
        className={`relative cursor-pointer ${
          orderbookType === EOrderbook.BID
            ? "hover:lg:border-b"
            : "hover:lg:border-t"
        } border-white-100 border-dashed`}
        onClick={handleClickItem}
      >
        <div className={`grid ${isHideTotal ? "grid-cols-2" : "grid-cols-3"}`}>
          <div
            className={`body-sm-regular-12 px-2 py-1 ${textColor} ${
              orderbookType == EOrderbook.BID && isHideTotal ? "order-2" : ""
            }`}
          >
            {formatNumberExact(item.price, getDecimalPlaces(decimal))}
          </div>
          <div
            className={`${
              orderbookType == EOrderbook.BID && isHideTotal
                ? "order-1"
                : "text-right"
            } body-sm-regular-12 px-2 py-1 `}
          >
            {formatNumberExact(item.amount, quantityDecimal)}
          </div>

          {!isHideTotal && (
            <div className="body-sm-regular-12 px-2 py-1 text-right">
              <AppNumber value={item?.total || 0} decimals={quantityDecimal} />
            </div>
          )}
        </div>
        <div
          className="absolute bottom-0 right-0 top-0 z-[1] h-full"
          style={{ width: `${percentWidth}%`, backgroundColor: bgColorVar }}
        />
      </div>
    );
  }
);
OrderRow.displayName = "OrderRow";

export const ListOrder = React.memo(
  ({
    orderbook,
    decimal,
    orderbookType,
    displayItemNumber,
    isHideTotal = false,
  }: {
    orderbook: OrderBookLevel[];
    decimal: number;
    orderbookType: EOrderbook;
    displayItemNumber: number;
    isHideTotal?: boolean;
  }) => {
    const [hoveredIndex, setHoveredIndex] = useState<null | number>(null);
    const { orderbookData, largestAmount } = useProcessedOrderbook(
      orderbook,
      decimal,
      displayItemNumber,
      orderbookType === EOrderbook.ASK
    );

    const isMobile = useMediaQuery({ query: "(max-width: 992px)" });
    const { pairSetting } = usePairContext();
    const { type } = useSelector(
      (state: RootState) => state.metadata.settingsLayout
    );
    const isLayoutAdvanced = type === TYPE_LAYOUT.ADVANCED;
    // need reserve in case ask side is not mobile
    const needReserve = orderbookType === EOrderbook.ASK && !isMobile;

    const [tooltipData, setTooltipData] = useState<{
      totalQuantity: string;
      totalAmount: string;
      avgPrice: string;
    }>({
      totalQuantity: "0",
      totalAmount: "0",
      avgPrice: "0",
    });

    const calculatePercentWidth = useCallback(
      (total: string) => {
        return BigNumber(total)
          .div(largestAmount || 1)
          .multipliedBy(100)
          .toFixed(0);
      },
      [largestAmount]
    );

    const calculateCumulativeData = useCallback(
      (targetIndex: number) => {
        if (!orderbookData)
          return {
            totalQuantity: new BigNumber(0),
            totalAmount: new BigNumber(0),
          };

        let totalQuantity = new BigNumber(0);
        let totalAmount = new BigNumber(0);

        for (let i = 0; i <= targetIndex; i++) {
          const item = orderbookData[i];
          if (item) {
            totalQuantity = totalQuantity.plus(item.amount);
            totalAmount = totalAmount.plus(item.total);
          }
        }

        return { totalQuantity, totalAmount };
      },
      [orderbookData]
    );

    const calculateTooltipData = useCallback(
      (hoveredIndex: number) => {
        const { totalQuantity, totalAmount } =
          calculateCumulativeData(hoveredIndex);

        const avgPrice = totalQuantity.isZero()
          ? new BigNumber(0)
          : totalAmount.div(totalQuantity);

        return {
          totalQuantity: totalQuantity.toFixed(),
          totalAmount: totalAmount.toFixed(),
          avgPrice: avgPrice.toFixed(),
        };
      },
      [calculateCumulativeData]
    );

    const handleItemClick = useCallback(
      (index: number, price: string) => {
        const { totalQuantity } = calculateCumulativeData(index);

        AppBroadcast.dispatch(BROADCAST_EVENTS.ORDERBOOK_SELECTED, {
          price,
          amount: totalQuantity.toFixed(),
          orderbookType,
        });
      },
      [calculateCumulativeData, orderbookType]
    );

    const handleRowHover = useCallback(
      (index: number) => {
        setHoveredIndex(index);
        const tooltipData = calculateTooltipData(index);

        setTooltipData(tooltipData);
      },
      [calculateTooltipData]
    );

    const getBg = (index: number) => {
      if (hoveredIndex !== null && index <= hoveredIndex) {
        return "lg:bg-white-50";
      }
      return "";
    };

    return (
      <div className={needReserve ? "flex flex-col-reverse" : ""}>
        {orderbookData?.map((item, index) => {
          const percentWidth = calculatePercentWidth(item.total);
          return (
            <Tippy
              key={index}
              content={
                <div className="z-[999] flex hidden min-w-[140px] flex-col gap-1 lg:block ">
                  <div className="flex justify-between">
                    <span className="body-sm-regular-12 text-white-500">
                      Avg Price:
                    </span>
                    <span className="body-sm-medium-12 text-white">
                      {formatPrice(
                        tooltipData.avgPrice,
                        pairSetting?.pricePrecision
                      )}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="body-sm-regular-12 text-white-500">
                      Sum Quantity:
                    </span>
                    <span className="body-sm-medium-12 text-white">
                      {formatNumber(
                        tooltipData.totalQuantity,
                        pairSetting?.quantityPrecision
                      )}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="body-sm-regular-12 text-white-500">
                      Sum Total:
                    </span>
                    <span className="body-sm-medium-12 text-white">
                      {formatNumber(tooltipData.totalAmount, 4)}
                    </span>
                  </div>
                </div>
              }
              placement={isLayoutAdvanced ? "left" : "right"}
              arrow={true}
              delay={[0, 0]}
              animation={false}
            >
              <div
                onMouseEnter={() => handleRowHover(index)}
                onMouseLeave={() => setHoveredIndex(null)}
                className={`${getBg(index)}`}
              >
                <OrderRow
                  key={`${item.price}-${index}`}
                  item={item}
                  isHideTotal={isHideTotal}
                  percentWidth={percentWidth}
                  orderbookType={orderbookType}
                  quantityDecimal={pairSetting?.quantityPrecision || 1}
                  decimal={decimal}
                  index={index}
                  onItemClick={handleItemClick}
                />
              </div>
            </Tippy>
          );
        })}
      </div>
    );
  }
);
ListOrder.displayName = "ListOrder";
