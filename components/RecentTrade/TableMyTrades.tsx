import { memo, useEffect, useRef, useState } from "react";
import { useMediaQuery } from "react-responsive";
import { Virtuoso } from "react-virtuoso";
import { formatUnixTimestamp } from "@/utils/format";
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from "@/libs/broadcast";
import { PairSetting } from "@/types/pair";
import RecentTradesHandler, {
  TTradeRecent,
} from "./services/RecentTradesHandler";
import AppNumber from "../AppNumber";

export const TableMyTrades = memo(
  ({ pairSetting }: { pairSetting: PairSetting | null }) => {
    const [forceRender, setForceRender] = useState(0);

    const handlerRef = useRef<RecentTradesHandler | null>(null);
    const tradesRef = useRef<TTradeRecent[]>([]);
    const frameIdRef = useRef<number | null>(null);

    const isMobile = useMediaQuery({ query: "(max-width: 992px)" });

    useEffect(() => {
      if (!pairSetting?.symbol) return;

      const onUpdateTrade = (updatedTrades: TTradeRecent[]) => {
        tradesRef.current = updatedTrades;

        if (frameIdRef.current !== null) {
          cancelAnimationFrame(frameIdRef.current);
        }

        frameIdRef.current = requestAnimationFrame(() => {
          setForceRender((prev) => prev + 1);
          frameIdRef.current = null;
        });
      };

      const handleTradeEvent = (data: TBroadcastEvent) => {
        if (handlerRef.current && data.detail) {
          handlerRef.current.formatAndUpdateUserTradeEvent(data.detail);
        }
      };

      AppBroadcast.on(BROADCAST_EVENTS.USER_TRADE_UPDATE, handleTradeEvent);

      handlerRef.current = new RecentTradesHandler(
        pairSetting?.symbol,
        onUpdateTrade
      );

      handlerRef.current.initUserTrades();

      return () => {
        if (frameIdRef.current !== null) {
          cancelAnimationFrame(frameIdRef.current);
        }
        AppBroadcast.remove(
          BROADCAST_EVENTS.USER_TRADE_UPDATE,
          handleTradeEvent
        );
      };
    }, [pairSetting?.symbol]);

    return (
      <>
        <div className="grid grid-cols-3">
          <div className="body-sm-regular-12 text-white-500 p-2">
            Price ({pairSetting?.quoteAsset?.toUpperCase()})
          </div>
          <div className="body-sm-regular-12 text-white-500 p-2 text-right">
            Amount ({pairSetting?.baseAsset?.toUpperCase()})
          </div>
          <div className="body-sm-regular-12 text-white-500 p-2 text-right">
            Time
          </div>
        </div>

        <div>
          <Virtuoso
            className="customer-scroll"
            style={{ height: isMobile ? 400 : 300 }}
            data={tradesRef.current}
            key={`my-trades-virtuoso-${pairSetting?.symbol}`}
            itemContent={(index, item) => (
              <div
                key={`trade-${item.id}-${index}`}
                className="hover:bg-white-50 grid cursor-pointer grid-cols-3"
              >
                <div
                  className="body-sm-regular-12 px-2 py-1"
                  style={{
                    color: item.isBuyer
                      ? "var(--up-color)"
                      : "var(--down-color)",
                  }}
                >
                  <AppNumber
                    value={item.price}
                    decimals={pairSetting?.pricePrecision}
                    isFormatLargeNumber={false}
                  />
                </div>
                <div className="body-sm-regular-12 px-2 py-1 text-right">
                  <AppNumber
                    value={item.quantity}
                    decimals={pairSetting?.quantityPrecision}
                  />
                </div>
                <div className="body-sm-regular-12 !font-rotobo-mono px-2 py-1 text-right">
                  {formatUnixTimestamp(item.time, "HH:mm:ss")}
                </div>
              </div>
            )}
          />
        </div>
      </>
    );
  }
);

TableMyTrades.displayName = "TableMyTrades";
