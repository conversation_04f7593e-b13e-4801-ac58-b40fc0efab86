"use client";

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components";
import {
  CloseIcon24,
  ChevronDownIcon,
  SettingColorDownUpIcon,
  CheckIcon,
} from "@/assets/icons";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/index";
import { setSettingsLayout } from "@/store/metadata.store";
import React, { useEffect, useState } from "react";
import { THEME_OPTIONS, TYPE_LAYOUT } from "../constants/common";
import { AppPopover } from "@/components";

const STYLE_PREFERENCES = [
  { id: "green-up", name: "Green Up / Red Down" },
  { id: "green-down", name: "Green Down / Red Up" },
];

const SelectColor = () => {
  const [isShow, setIsShow] = useState<boolean>(false);
  const [selected, setSelected] = useState("fresh");
  const settingsLayout = useSelector(
    (state: RootState) => state.metadata.settingsLayout
  );
  const dispatch = useDispatch();

  useEffect(() => {
    setSelected(settingsLayout.theme || "fresh");
  }, [settingsLayout]);

  const handleSelect = (id: string) => {
    setSelected(id);
    dispatch(
      setSettingsLayout({
        settingsLayout: {
          ...settingsLayout,
          theme: id,
        },
      })
    );

    setIsShow(false);
  };

  const selectedOption = THEME_OPTIONS.find((opt) => opt.id === selected);

  return (
    <AppPopover
      position="left"
      trigger={
        <div className="flex cursor-pointer items-center gap-4">
          <div className="flex items-center gap-1">
            {selectedOption?.colors?.map((c, i) => (
              <span
                key={i}
                className="h-4 w-4 rounded-[2px]"
                style={{ backgroundColor: c }}
              />
            ))}
          </div>
          <div className="body-sm-medium-12">{selectedOption?.name}</div>
          <div>
            <ChevronDownIcon className={isShow ? "rotate-[180deg]" : ""} />
          </div>
        </div>
      }
      onClose={() => setIsShow(false)}
      content={
        <div
          style={{
            boxShadow: "4px 4px 8px 0px var(--Black-500, rgba(8, 9, 12, 0.50))",
            background:
              "linear-gradient(0deg, var(--White-100, rgba(255, 255, 255, 0.10)) 0%, var(--White-100, rgba(255, 255, 255, 0.10)) 100%), var(--Black-900, #08090C)",
          }}
          className="flex min-w-[240px] flex-col rounded-[8px] p-1"
        >
          <div className="flex flex-col gap-3 p-2">
            {THEME_OPTIONS.map((item, index) => {
              return (
                <div
                  key={index}
                  className="flex cursor-pointer items-center justify-between gap-4"
                  onClick={() => handleSelect(item.id)}
                >
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-1">
                      {item.colors.map((c, i) => (
                        <span
                          key={i}
                          className="h-4 w-4 rounded-[2px]"
                          style={{ backgroundColor: c }}
                        />
                      ))}
                    </div>
                    <div className="body-sm-medium-12">{item.name}</div>
                  </div>

                  {selected === item.id && (
                    <CheckIcon className="text-green-400" />
                  )}
                </div>
              );
            })}
          </div>
        </div>
      }
      isOpen={isShow}
      onToggle={() => setIsShow(!isShow)}
    />
  );
};

const SelectStyle = () => {
  const [isShow, setIsShow] = useState<boolean>(false);
  const [selected, setSelected] = useState("green-up");
  const settingsLayout = useSelector(
    (state: RootState) => state.metadata.settingsLayout
  );
  const dispatch = useDispatch();

  useEffect(() => {
    setSelected(settingsLayout.style || "green-up");
  }, [settingsLayout]);

  const handleSelect = (id: string) => {
    setSelected(id);
    dispatch(
      setSettingsLayout({
        settingsLayout: {
          ...settingsLayout,
          style: id,
        },
      })
    );

    setIsShow(false);
  };

  const selectedOption = STYLE_PREFERENCES.find((opt) => opt.id === selected);

  return (
    <AppPopover
      position="left"
      trigger={
        <div className="body-sm-medium-12 flex cursor-pointer items-center gap-2">
          <SettingColorDownUpIcon
            className={`-mr-3 rotate-[180deg] text-[color:var(--up-color)]`}
          />
          <SettingColorDownUpIcon
            className={"text-[color:var(--down-color)]"}
          />
          {selectedOption?.name}
          <div>
            <ChevronDownIcon />
          </div>
        </div>
      }
      onClose={() => setIsShow(false)}
      content={
        <div
          style={{
            boxShadow: "4px 4px 8px 0px var(--Black-500, rgba(8, 9, 12, 0.50))",
            background:
              "linear-gradient(0deg, var(--White-100, rgba(255, 255, 255, 0.10)) 0%, var(--White-100, rgba(255, 255, 255, 0.10)) 100%), var(--Black-900, #08090C)",
          }}
          className="flex min-w-[200px] flex-col rounded-[8px] p-1"
        >
          <div className="flex flex-col gap-3 p-2">
            {STYLE_PREFERENCES.map((item, index) => {
              return (
                <div
                  key={index}
                  className="flex cursor-pointer items-center justify-between gap-4"
                  onClick={() => handleSelect(item.id)}
                >
                  <div className="body-sm-medium-12 flex cursor-pointer items-center gap-2">
                    <SettingColorDownUpIcon
                      className={`${
                        item.id === "green-up"
                          ? "text-[color:var(--up-color)]"
                          : "text-[color:var(--down-color)]"
                      } -mr-3 rotate-[180deg]`}
                    />
                    <SettingColorDownUpIcon
                      className={
                        item.id === "green-up"
                          ? "text-[color:var(--down-color)]"
                          : "text-[color:var(--up-color)]"
                      }
                    />
                    {item?.name}
                  </div>
                  {selected === item.id && (
                    <CheckIcon className="text-green-400" />
                  )}
                </div>
              );
            })}
          </div>
        </div>
      }
      isOpen={isShow}
      onToggle={() => setIsShow(!isShow)}
    />
  );
};

export const ModalSettingLayout = ({
  isOpen,
  onClose,
}: {
  isOpen: boolean;
  onClose: () => void;
}) => {
  const [settingsLayoutAdvanced, setSettingsLayoutAdvanced] = useState<any>({});

  const dispatch = useDispatch();
  const settingsLayout = useSelector(
    (state: RootState) => state.metadata.settingsLayout
  );

  useEffect(() => {
    setSettingsLayoutAdvanced(settingsLayout);
  }, [settingsLayout]);

  return (
    <AppDrawer
      isOpen={isOpen}
      className="border-white-100 !lg:w-[420px] !lg:h-screen !bottom-0 !top-auto !z-[9999] !w-[375px] border-l !bg-[#212225]"
      enableOverlay={false}
    >
      <div className="flex h-full flex-col justify-between">
        <div>
          <div className="border-white-50 flex items-center justify-between border-b px-4 py-2.5">
            <div className="body-md-medium-14">Layout</div>
            <CloseIcon24 onClick={onClose} className="cursor-pointer" />
          </div>

          <div className="p-4">
            <div>
              <div className="heading-sm-medium-16 text-white-500">Style</div>
              <div className="flex items-center justify-between py-4">
                <div className="heading-sm-medium-16">Color reference</div>
                <SelectColor />
              </div>
              <div className="flex items-center justify-between py-4">
                <div className="heading-sm-medium-16">Style Settings</div>
                <SelectStyle />
              </div>
            </div>
          </div>
          <div className="p-4">
            <div>
              <div className="heading-sm-medium-16 text-white-500">
                Order Mode
              </div>
              <div className="grid grid-cols-2 gap-2 py-4">
                <div className="flex w-full flex-col items-center gap-2">
                  <div
                    onClick={() => {
                      dispatch(
                        setSettingsLayout({
                          settingsLayout: {
                            ...settingsLayout,
                            type: TYPE_LAYOUT.CLASSIC,
                          },
                        })
                      );
                      onClose();
                    }}
                    className={`bg-white-25 hover:border-brand-500 flex h-[80px] w-full cursor-pointer items-end justify-center rounded-[8px] border p-1 ${
                      settingsLayout?.type === TYPE_LAYOUT.CLASSIC
                        ? "border-brand-500"
                        : "border-white-100"
                    }`}
                  >
                    <div className="bg-white-50 flex gap-[2px] rounded-[6px] p-1 pt-4">
                      <div className="h-1 w-[23px] rounded-[1px] bg-[color:var(--up-color)]"></div>
                      <div className="h-1 w-[23px] rounded-[1px] bg-[color:var(--down-color)]"></div>
                    </div>
                  </div>
                  <div className="heading-sm-medium-16">Classic</div>
                </div>

                <div className="flex w-full flex-col items-center gap-2">
                  <div
                    onClick={() => {
                      dispatch(
                        setSettingsLayout({
                          settingsLayout: {
                            ...settingsLayout,
                            type: TYPE_LAYOUT.ADVANCED,
                          },
                        })
                      );
                      onClose();
                    }}
                    className={`bg-white-25 hover:border-brand-500 flex h-[80px] w-full cursor-pointer justify-end rounded-[8px] border p-1 ${
                      settingsLayout?.type === TYPE_LAYOUT.ADVANCED
                        ? "border-brand-500"
                        : "border-white-100"
                    }`}
                  >
                    <div className="bg-white-50 flex h-full w-[40px] flex-col justify-between rounded-[6px] p-1">
                      <div className="flex gap-[2px]">
                        <div className="h-1 w-[23px] rounded-[1px] bg-[color:var(--up-color)]"></div>
                        <div className="h-1 w-[23px] rounded-[1px] bg-[color:var(--down-color)]"></div>
                      </div>
                      <div className="h-1 w-full rounded-[1px] bg-[color:var(--up-color)]"></div>
                    </div>
                  </div>
                  <div className="heading-sm-medium-16">Advanced</div>
                </div>
              </div>
            </div>
          </div>

          {settingsLayout?.type === TYPE_LAYOUT.ADVANCED && (
            <div className="p-4 pb-0">
              <div className="border-white-50 border-t">
                {/*<div className="flex items-center justify-between py-2">*/}
                {/*  <div className="heading-sm-medium-16">Favourite</div>*/}
                {/*  <AppToggle*/}
                {/*    value={settingsLayoutAdvanced?.isHasFavourite}*/}
                {/*    onChange={() =>*/}
                {/*      dispatch(*/}
                {/*        setSettingsLayout({*/}
                {/*          settingsLayout: {*/}
                {/*            ...settingsLayout,*/}
                {/*            isHasFavourite: !settingsLayout?.isHasFavourite,*/}
                {/*          },*/}
                {/*        })*/}
                {/*      )*/}
                {/*    }*/}
                {/*  />*/}
                {/*</div>*/}
                <div className="flex items-center justify-between py-2">
                  <div className="heading-sm-medium-16">Chart</div>
                  <AppToggle
                    value={settingsLayoutAdvanced?.isHasChart}
                    onChange={() =>
                      dispatch(
                        setSettingsLayout({
                          settingsLayout: {
                            ...settingsLayout,
                            isHasChart: !settingsLayout?.isHasChart,
                          },
                        })
                      )
                    }
                  />
                </div>
                <div className="flex items-center justify-between py-2">
                  <div className="heading-sm-medium-16">Order Book</div>
                  <AppToggle
                    value={settingsLayoutAdvanced?.isHasOrderBook}
                    onChange={() =>
                      dispatch(
                        setSettingsLayout({
                          settingsLayout: {
                            ...settingsLayout,
                            isHasOrderBook: !settingsLayout?.isHasOrderBook,
                          },
                        })
                      )
                    }
                  />
                </div>
                <div className="flex items-center justify-between py-2">
                  <div className="heading-sm-medium-16">Trades</div>
                  <AppToggle
                    value={settingsLayoutAdvanced?.isHasTrades}
                    onChange={() =>
                      dispatch(
                        setSettingsLayout({
                          settingsLayout: {
                            ...settingsLayout,
                            isHasTrades: !settingsLayout?.isHasTrades,
                          },
                        })
                      )
                    }
                  />
                </div>
                <div className="flex items-center justify-between py-2">
                  <div className="heading-sm-medium-16">Open Orders</div>
                  <AppToggle
                    value={settingsLayoutAdvanced?.isHasOpenOrders}
                    onChange={() =>
                      dispatch(
                        setSettingsLayout({
                          settingsLayout: {
                            ...settingsLayout,
                            isHasOpenOrders: !settingsLayout?.isHasOpenOrders,
                          },
                        })
                      )
                    }
                  />
                </div>
                <div className="flex items-center justify-between py-2">
                  <div className="heading-sm-medium-16">Place Order</div>
                  <AppToggle
                    value={settingsLayoutAdvanced?.isHasPlaceOrder}
                    onChange={() =>
                      dispatch(
                        setSettingsLayout({
                          settingsLayout: {
                            ...settingsLayout,
                            isHasPlaceOrder: !settingsLayout?.isHasPlaceOrder,
                          },
                        })
                      )
                    }
                  />
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="p-4">
          <AppButton
            size="large"
            variant="secondary"
            onClick={() => {
              dispatch(
                setSettingsLayout({
                  settingsLayout: {
                    ...settingsLayout,
                    type: TYPE_LAYOUT.CLASSIC,
                  },
                })
              );
              onClose();
            }}
          >
            Back to Default Layout
          </AppButton>
        </div>
      </div>
    </AppDrawer>
  );
};
