import { TYPE_LAYOUT } from "../constants/common";

const PREFERENCES = `vdax`;

type StorageInterface = {
  accessToken?: string;
  refreshToken?: string;
  settingsLayout: {
    theme: string;
    style: string;
    type: string;
    isHasChart: boolean;
    isHasOrderBook: boolean;
    isHasTrades: boolean;
    isHasOpenOrders: boolean;
    isHasPlaceOrder: boolean;
  };
};

const defaultPreferences: StorageInterface = {
  settingsLayout: {
    theme: "fresh",
    style: "green-up",
    type: TYPE_LAYOUT.CLASSIC,
    isHasChart: true,
    isHasOrderBook: true,
    isHasTrades: true,
    isHasOpenOrders: true,
    isHasPlaceOrder: true,
  },
};

function getStorage(): StorageInterface | any {
  if (typeof window === "undefined") return {};
  const preferencesString = localStorage.getItem(PREFERENCES);
  const preferences = JSON.parse(preferencesString || "{}");
  return {
    ...defaultPreferences,
    ...preferences,
  };
}

function setStorage(type: string, value: StorageInterface) {
  if (typeof window === "undefined") return;
  localStorage.setItem(type, JSON.stringify(value));
}

class Storage {
  static init() {
    const preferences = getStorage();
    setStorage(PREFERENCES, preferences);
  }

  static getAccessToken(): string | undefined {
    const { accessToken } = getStorage();
    return accessToken;
  }

  static setAccessToken(accessToken: string) {
    const preferences = getStorage();
    preferences.accessToken = accessToken;
    setStorage(PREFERENCES, preferences);
  }

  static getRefreshToken(): string | undefined {
    const { refreshToken } = getStorage();
    return refreshToken;
  }

  static setRefreshToken(refreshToken: string) {
    const preferences = getStorage();
    preferences.refreshToken = refreshToken;
    setStorage(PREFERENCES, preferences);
  }

  static getSettingsLayout(): string | undefined {
    const { settingsLayout } = getStorage();
    return settingsLayout;
  }

  static setSettingsLayout(settingsLayout: any) {
    const preferences = getStorage();
    preferences.settingsLayout = settingsLayout;
    setStorage(PREFERENCES, preferences);
  }
}

export default Storage;
