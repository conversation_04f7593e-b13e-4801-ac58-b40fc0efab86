import { EOrderType } from "@/components/OrderForm";
import { EOrderSide } from "@/components/OrderForm/OrderFormMobile";

export enum EOrderStopCondition {
  GREATER_THAN = "GREATER_THAN",
  LESS_THAN = "LESS_THAN",
}

export type TOrderPayload = {
  price?: string;
  quantity: string | null;
  side: EOrderSide;
  symbol: string;
  type: EOrderType;
  stopPrice?: string;
  stopCondition?: EOrderStopCondition;
  quoteOrderQty?: string | null;
};

export type TOpenOrderParams = {
  symbol: string;
};

export type TPaginationParams = {
  cursor?: number;
  limit?: number;
  order?: "desc" | "asc";
};

export type TOrderParams = {
  price?: string;
  quantity: string;
  side: EOrderSide;
  symbol: string;
  type: string;
};

export type TOrderResponse = {
  order_id: string;
  user_id: number;
  symbol: string;
  side: EOrderSide;
  type: EOrderType;
  price: string | null;
  orig_qty: string;
};

export enum TOrderAction {
  PLACE = "place",
  CANCEL = "cancel",
}
export enum EOrderStatus {
  NEW = "NEW",
  PARTIALLY_FILLED = "PARTIALLY_FILLED",
  FILLED = "FILLED",
  CANCELLED = "CANCELLED",
  REJECTED = "REJECTED",
}

export enum EOrderStatusKey {
  NEW,
  PARTIALLY_FILLED,
  FILLED,
  CANCELED,
  REJECTED,
}

export enum EOrderTypeKey {
  LIMIT,
  MARKET,
  STOP_LIMIT,
  STOP_MARKET,
}

export type TOpenOrder = {
  id: string;
  order_id: string;
  user_id: number;
  symbol: string;
  side: EOrderSide;
  type: EOrderType;
  price: number | null;
  orig_qty: string;
  executed_qty: string | null;
  cummulative_quote_qty: string | null;
  status: EOrderStatus;
  me_output_at: number;
  created_at: number;
  updated_at: number;
  canceled_at: string;
  stop_price?: string | null;
  stop_condition?: EOrderStopCondition | null;
  triggered_at?: number | null;
};

export type TOrderHistory = {
  id: string;
  user_id: number;
  symbol: string;
  side: EOrderSide;
  type: EOrderType;
  price: string | null;
  orig_qty: string;
  executed_qty: string | null;
  cummulative_quote_qty: string | null;
  status: EOrderStatus;
  me_output_at: number;
  created_at: number;
  updated_at: number;
  stop_price?: string | null;
  stop_condition?: EOrderStopCondition | null;
};

export type TOrderUpdated = {
  i: string; // Order ID
  u: number; // User ID
  s: string; // Symbol
  B: string; // Base asset
  Q: string; // Quote asset

  S: 0 | 1; // Side (0 = BUY, 1 = SELL)
  o: number; // Order Type (e.g., 0 = LIMIT, 1 = MARKET, etc.)
  A: number; // Status (e.g., 0 = NEW, 1 = PARTIALLY_FILLED, etc.)
  t: number; // Time in force (0 = GTC, 1 = IOC, 2 = FOK)

  p: string; // Price
  q: string; // Original quantity
  Y: string; // Executed quantity
  Z: string; // Cumulative quote quantity
  M: string | null; // Optional timestamp or null

  C: string; // Cancelled at

  st: string; // STOP CONDITION
  sp: string; // STOP PRICE
};
