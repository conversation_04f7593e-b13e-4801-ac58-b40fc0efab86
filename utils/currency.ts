import BigNumber from "bignumber.js";
import { Ticker } from "@/types/pair";

export const convertToUSDTPrice = (
  currentPrice: string | null,
  quoteAsset: string | undefined,
  tickers: { [symbol: string]: Ticker }
): string | null => {
  if (!currentPrice || !quoteAsset) {
    return null;
  }

  if (quoteAsset.toUpperCase() === "USDT") {
    return currentPrice;
  }

  const conversionSymbol = `${quoteAsset.toUpperCase()}USDT`;
  const conversionTicker = tickers[conversionSymbol];

  if (!conversionTicker?.lastPrice) {
    console.warn(
      `No conversion rate available for ${conversionSymbol}. Cannot convert ${quoteAsset} to USDT.`
    );
    return null;
  }

  try {
    const usdtPrice = BigNumber(currentPrice)
      .multipliedBy(conversionTicker.lastPrice)
      .toFixed();
    return usdtPrice;
  } catch (error) {
    console.error("Error converting price to USDT:", error);
    return null;
  }
};

export const isUSDTQuoted = (quoteAsset: string | undefined): boolean => {
  return quoteAsset?.toUpperCase() === "USDT";
};

export const getUSDTConversionSymbol = (quoteAsset: string): string => {
  return `${quoteAsset.toUpperCase()}USDT`;
};
