import { BaseModal } from "@/modals/BaseModal";
import React from "react";
import { AppButton } from "@/components";
import { CloseIcon24 } from "@/assets/icons";
import { useMediaQuery } from "react-responsive";

export const ModalComplianceTips = ({
  isOpen,
  onClose,
}: {
  isOpen: boolean;
  onClose: () => void;
}) => {
  const isMobile = useMediaQuery({ query: "(max-width: 768px)" });

  return (
    <BaseModal
      className="w-screen md:w-[500px]"
      isOpen={isOpen}
      onClose={onClose}
      isBottom={isMobile}
    >
      <div className="flex items-center justify-between gap-4 pb-4">
        <div className="heading-md-semibold-18">Compliance Tips</div>
        <div className="cursor-pointer" onClick={onClose}>
          <CloseIcon24 />
        </div>
      </div>

      <div className="customer-scroll body-sm-regular-12 h-[300px] overflow-y-auto">
        <div className="body-md-medium-14">
          Transacting with Sanctioned Entities
        </div>
        <br /> Is Prohibited Your security is our top priority at VDAX. Please
        be reminded of the following essential information when sending and
        receiving funds through our platform.
        <br />
        <br /> We remind all our users not to transact with any entities that
        have been sanctioned by regulatory authorities. This includes sending or
        receiving funds to your VDAX account from cryptocurrency exchanges that
        have been designated as sanctioned entities by governments and
        international organizations. Entering into such prohibited transactions
        may impact access to your VDAX account. <br />
        <br />
        <div className="body-md-medium-14">
          Sanctioned Cryptocurrency Exchanges
        </div>
        <br /> For your ease of reference, we have compiled a non-comprehensive
        list of cryptocurrency exchanges that have been currently placed under
        sanctions or flagged by the respective authorities. This is public
        information that will be updated from time to time. Please exercise
        extreme caution when dealing with any of these exchanges:
        <br />
        <br /> 1. Garantex
        <br />
        <br /> 2. Suex <br />
        <br />
        3. Chatex
        <br /> <br />
        <div className="body-md-medium-14">
          Exercise Caution with All Transactions
        </div>
        <br />
        In addition, to avoid transacting with sanctioned entities, we strongly
        advise you to exercise caution in respect of who you send or receive
        funds from. Always double-check the legitimacy of the parties involved
        in your transactions.
        <br />
        <br /> If you have any doubts or concerns about a particular transaction
        or entity, please do not hesitate to contact our customer support team.
        We are here to assist you and provide guidance to ensure the safety and
        security of your account. <br />
        <br />
        Thank you for choosing VDAX. We appreciate your cooperation in
        maintaining the integrity of our platform and ensuring the safety of all
        our users. <br />
        <br />
        Sincerely,
        <br />
        <br /> VDAX Compliance Team
      </div>

      <div className="mt-6">
        <AppButton
          size="large"
          variant="buy"
          onClick={onClose}
          className="w-full"
        >
          OK
        </AppButton>
      </div>
    </BaseModal>
  );
};
