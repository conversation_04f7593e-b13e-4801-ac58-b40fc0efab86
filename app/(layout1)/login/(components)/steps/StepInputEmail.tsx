"use client";

import { GoogleIcon } from "@/assets/icons";
import { AppButton, EmailInput } from "@/components";
import Link from "next/link";
import { useContext, useRef } from "react";
import { ELoginStep } from "../steps";
import { handleSocialLogin } from "@/utils/auth";
import { ELoginProvider } from "@/types/auth";
import { LoginStepContext } from "../contexts/LoginStepContext";
import { EmailInputRef } from "@/components/EmailInput";

type StepInputEmailProps = {
  setStep: (step: ELoginStep) => void;
  mailOrPhone: string;
  setMailOrPhone: (mailOrPhone: string) => void;
};

export const StepInputEmail: React.FC<StepInputEmailProps> = ({
  setStep,
  mailOrPhone,
  setMailOrPhone,
}) => {
  const { redirectUrl } = useContext(LoginStepContext)!;
  const emailInputRef = useRef<EmailInputRef>(null);

  const onNext = async () => {
    if (!emailInputRef.current?.validate()) {
      return;
    }

    setStep(ELoginStep.InputPassword);
  };

  return (
    <>
      <div className="heading-lg-semibold-24 lg:text-[24px]">Log In</div>

      <EmailInput
        ref={emailInputRef}
        value={mailOrPhone}
        onChange={setMailOrPhone}
        onSubmit={onNext}
        autoFocus
        autoComplete="off"
      />

      <AppButton
        variant="buy"
        size="large"
        onClick={onNext}
        disabled={!mailOrPhone.trim()}
      >
        Next
      </AppButton>

      <div className="flex items-center gap-1">
        <div className="bg-white-100 h-[1px] w-[calc(50%-4px)]"></div>
        <div className="body-md-regular-14 text-white-800">Or</div>
        <div className="bg-white-100 h-[1px] w-[calc(50%-4px)]"></div>
      </div>

      <AppButton
        variant="outline"
        size="large"
        className="relative"
        onClick={() => handleSocialLogin(ELoginProvider.GOOGLE, redirectUrl)}
      >
        <GoogleIcon className="absolute left-4" /> Continue with Google
      </AppButton>

      <div className="flex justify-center py-2.5">
        <Link href="/register">
          <div className="body-md-medium-14 text-brand-500 cursor-pointer">
            Create a VDAX Account
          </div>
        </Link>
      </div>
    </>
  );
};
