"use client";
import { ReactNode, useEffect } from "react";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { LayoutProvider } from "./context/LayoutContext";
import { useSelector } from "react-redux";
import { RootState } from "../../store/index";

interface BaseLayoutProps {
  children: ReactNode;
}

export const BaseLayout = ({ children }: BaseLayoutProps) => {
  const settingsLayout = useSelector(
    (state: RootState) => state.metadata.settingsLayout
  );

  useEffect(() => {
    document.documentElement.setAttribute("data-theme", settingsLayout?.theme);
    document.documentElement.setAttribute("data-style", settingsLayout?.style);
  }, [settingsLayout?.theme, settingsLayout?.style]);

  return (
    <LayoutProvider>
      <ToastContainer
        autoClose={2000}
        position="top-right"
        icon={false}
        pauseOnHover
        closeButton={false}
        hideProgressBar
      />
      <div className="min-h-screen">{children}</div>
    </LayoutProvider>
  );
};
